"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import ProtectedRoute from "@/components/protected-route"
import { supabase } from "@/lib/supabase/client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Loader2, PlusCircle, Edit, Trash2, LogOut, BarChart3, FileText, Users, Settings } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"

type BlogPost = {
  id: string
  title: string
  slug: string
  category: string | null
  published: boolean
  created_at: string
  updated_at: string | null
}

type DashboardStats = {
  totalPosts: number
  publishedPosts: number
  draftPosts: number
  categories: { [key: string]: number }
}

export default function AdminDashboard() {
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [stats, setStats] = useState<DashboardStats>({
    totalPosts: 0,
    publishedPosts: 0,
    draftPosts: 0,
    categories: {},
  })
  const [loading, setLoading] = useState(true)
  const { user, signOut } = useAuth()
  const router = useRouter()

  useEffect(() => {
    const fetchPosts = async () => {
      setLoading(true)

      try {
        const { data, error } = await supabase.from("blog_posts").select("*").order("created_at", { ascending: false })

        if (error) {
          console.error("Error fetching posts:", error)
          return
        }

        if (data) {
          setPosts(data)

          // Calculate dashboard stats
          const publishedPosts = data.filter((post) => post.published).length
          const draftPosts = data.filter((post) => !post.published).length

          // Count posts by category
          const categories: { [key: string]: number } = {}
          data.forEach((post) => {
            const category = post.category || "Uncategorized"
            categories[category] = (categories[category] || 0) + 1
          })

          setStats({
            totalPosts: data.length,
            publishedPosts,
            draftPosts,
            categories,
          })
        }
      } catch (err) {
        console.error("Error in fetchPosts:", err)
      } finally {
        setLoading(false)
      }
    }

    fetchPosts()
  }, [])

  const handleDeletePost = async (id: string) => {
    if (confirm("Are you sure you want to delete this post?")) {
      setLoading(true)
      const { error } = await supabase.from("blog_posts").delete().eq("id", id)

      if (error) {
        console.error("Error deleting post:", error)
      } else {
        setPosts(posts.filter((post) => post.id !== id))

        // Update stats
        const updatedPosts = posts.filter((post) => post.id !== id)
        const publishedPosts = updatedPosts.filter((post) => post.published).length
        const draftPosts = updatedPosts.filter((post) => !post.published).length

        // Recalculate categories
        const categories: { [key: string]: number } = {}
        updatedPosts.forEach((post) => {
          const category = post.category || "Uncategorized"
          categories[category] = (categories[category] || 0) + 1
        })

        setStats({
          totalPosts: updatedPosts.length,
          publishedPosts,
          draftPosts,
          categories,
        })
      }
      setLoading(false)
    }
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-muted/50">
        <header className="bg-background border-b sticky top-0 z-10">
          <div className="container flex h-16 items-center justify-between px-4">
            <div className="flex items-center gap-4">
              <Link
                href="/"
                className="text-xl font-bold bg-gradient-to-r from-purple-600 to-cyan-500 bg-clip-text text-transparent"
              >
                EarnifyAI
              </Link>
              <span className="text-sm text-muted-foreground">Admin Dashboard</span>
            </div>
            <div className="flex items-center gap-4">
              <span className="text-sm text-muted-foreground hidden md:inline-block">Logged in as: {user?.email}</span>
              <Button variant="outline" size="sm" onClick={() => signOut()}>
                <LogOut className="mr-2 h-4 w-4" />
                Sign Out
              </Button>
            </div>
          </div>
        </header>

        <div className="flex">
          {/* Sidebar */}
          <aside className="w-64 border-r bg-background hidden md:block h-[calc(100vh-4rem)] sticky top-16">
            <nav className="p-4 space-y-2">
              <div className="text-sm font-medium text-muted-foreground mb-4">MAIN MENU</div>
              <Link
                href="/admin/dashboard"
                className="flex items-center gap-3 px-3 py-2 text-primary font-medium rounded-md bg-primary/10"
              >
                <BarChart3 className="h-4 w-4" />
                Dashboard
              </Link>
              <Link
                href="/admin/posts/new"
                className="flex items-center gap-3 px-3 py-2 text-muted-foreground hover:text-primary rounded-md hover:bg-muted transition-colors"
              >
                <FileText className="h-4 w-4" />
                Posts
              </Link>
              <Link
                href="/admin/users"
                className="flex items-center gap-3 px-3 py-2 text-muted-foreground hover:text-primary rounded-md hover:bg-muted transition-colors"
              >
                <Users className="h-4 w-4" />
                Users
              </Link>
              <Link
                href="/admin/settings"
                className="flex items-center gap-3 px-3 py-2 text-muted-foreground hover:text-primary rounded-md hover:bg-muted transition-colors"
              >
                <Settings className="h-4 w-4" />
                Settings
              </Link>
            </nav>
          </aside>

          {/* Main content */}
          <main className="flex-1 p-6">
            <div className="flex items-center justify-between mb-8">
              <h1 className="text-3xl font-bold">Dashboard</h1>
              <Link href="/admin/posts/new">
                <Button>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  New Post
                </Button>
              </Link>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Total Posts</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">{stats.totalPosts}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Published</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-green-600">{stats.publishedPosts}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Drafts</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-amber-600">{stats.draftPosts}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Categories</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">{Object.keys(stats.categories).length}</div>
                </CardContent>
              </Card>
            </div>

            <Tabs defaultValue="all">
              <div className="flex items-center justify-between mb-4">
                <TabsList>
                  <TabsTrigger value="all">All Posts</TabsTrigger>
                  <TabsTrigger value="published">Published</TabsTrigger>
                  <TabsTrigger value="drafts">Drafts</TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="all">
                <Card>
                  <CardHeader>
                    <CardTitle>All Blog Posts</CardTitle>
                    <CardDescription>Manage all your blog posts from here</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {loading ? (
                      <div className="flex justify-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
                      </div>
                    ) : posts.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-muted-foreground">No posts found. Create your first post!</p>
                        <Link href="/admin/posts/new">
                          <Button variant="outline" className="mt-4">
                            <PlusCircle className="mr-2 h-4 w-4" />
                            Create Post
                          </Button>
                        </Link>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Title</TableHead>
                              <TableHead>Category</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Created</TableHead>
                              <TableHead>Updated</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {posts.map((post) => (
                              <TableRow key={post.id}>
                                <TableCell className="font-medium">{post.title}</TableCell>
                                <TableCell>{post.category || "Uncategorized"}</TableCell>
                                <TableCell>
                                  <Badge variant={post.published ? "default" : "outline"}>
                                    {post.published ? "Published" : "Draft"}
                                  </Badge>
                                </TableCell>
                                <TableCell>{format(new Date(post.created_at), "MMM d, yyyy")}</TableCell>
                                <TableCell>
                                  {post.updated_at ? format(new Date(post.updated_at), "MMM d, yyyy") : "-"}
                                </TableCell>
                                <TableCell className="text-right">
                                  <div className="flex justify-end gap-2">
                                    <Button
                                      variant="outline"
                                      size="icon"
                                      onClick={() => router.push(`/admin/posts/edit/${post.id}`)}
                                    >
                                      <Edit className="h-4 w-4" />
                                      <span className="sr-only">Edit</span>
                                    </Button>
                                    <Button variant="outline" size="icon" onClick={() => handleDeletePost(post.id)}>
                                      <Trash2 className="h-4 w-4" />
                                      <span className="sr-only">Delete</span>
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="published">
                <Card>
                  <CardHeader>
                    <CardTitle>Published Posts</CardTitle>
                    <CardDescription>View and manage your published blog posts</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {loading ? (
                      <div className="flex justify-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
                      </div>
                    ) : posts.filter((post) => post.published).length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-muted-foreground">No published posts found.</p>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Title</TableHead>
                              <TableHead>Category</TableHead>
                              <TableHead>Created</TableHead>
                              <TableHead>Updated</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {posts
                              .filter((post) => post.published)
                              .map((post) => (
                                <TableRow key={post.id}>
                                  <TableCell className="font-medium">{post.title}</TableCell>
                                  <TableCell>{post.category || "Uncategorized"}</TableCell>
                                  <TableCell>{format(new Date(post.created_at), "MMM d, yyyy")}</TableCell>
                                  <TableCell>
                                    {post.updated_at ? format(new Date(post.updated_at), "MMM d, yyyy") : "-"}
                                  </TableCell>
                                  <TableCell className="text-right">
                                    <div className="flex justify-end gap-2">
                                      <Button
                                        variant="outline"
                                        size="icon"
                                        onClick={() => router.push(`/admin/posts/edit/${post.id}`)}
                                      >
                                        <Edit className="h-4 w-4" />
                                        <span className="sr-only">Edit</span>
                                      </Button>
                                      <Button variant="outline" size="icon" onClick={() => handleDeletePost(post.id)}>
                                        <Trash2 className="h-4 w-4" />
                                        <span className="sr-only">Delete</span>
                                      </Button>
                                    </div>
                                  </TableCell>
                                </TableRow>
                              ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="drafts">
                <Card>
                  <CardHeader>
                    <CardTitle>Draft Posts</CardTitle>
                    <CardDescription>View and manage your unpublished draft posts</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {loading ? (
                      <div className="flex justify-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
                      </div>
                    ) : posts.filter((post) => !post.published).length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-muted-foreground">No draft posts found.</p>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Title</TableHead>
                              <TableHead>Category</TableHead>
                              <TableHead>Created</TableHead>
                              <TableHead>Updated</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {posts
                              .filter((post) => !post.published)
                              .map((post) => (
                                <TableRow key={post.id}>
                                  <TableCell className="font-medium">{post.title}</TableCell>
                                  <TableCell>{post.category || "Uncategorized"}</TableCell>
                                  <TableCell>{format(new Date(post.created_at), "MMM d, yyyy")}</TableCell>
                                  <TableCell>
                                    {post.updated_at ? format(new Date(post.updated_at), "MMM d, yyyy") : "-"}
                                  </TableCell>
                                  <TableCell className="text-right">
                                    <div className="flex justify-end gap-2">
                                      <Button
                                        variant="outline"
                                        size="icon"
                                        onClick={() => router.push(`/admin/posts/edit/${post.id}`)}
                                      >
                                        <Edit className="h-4 w-4" />
                                        <span className="sr-only">Edit</span>
                                      </Button>
                                      <Button variant="outline" size="icon" onClick={() => handleDeletePost(post.id)}>
                                        <Trash2 className="h-4 w-4" />
                                        <span className="sr-only">Delete</span>
                                      </Button>
                                    </div>
                                  </TableCell>
                                </TableRow>
                              ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </main>
        </div>
      </div>
    </ProtectedRoute>
  )
}
