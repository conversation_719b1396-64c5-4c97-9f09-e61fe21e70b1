"use client"

import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import ProtectedRoute from "@/components/protected-route"

export default function AdminSettingsPage() {
  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-muted/50 p-8">
        <Link href="/admin/dashboard" className="flex items-center text-sm font-medium mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Dashboard
        </Link>

        <h1 className="text-3xl font-bold mb-8">Settings</h1>

        <div className="bg-background rounded-lg p-8 text-center">
          <p className="text-muted-foreground">Settings functionality coming soon.</p>
        </div>
      </div>
    </ProtectedRoute>
  )
}
