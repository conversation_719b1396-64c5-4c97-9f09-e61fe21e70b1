"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { keywordStrategy } from "@/lib/seo/keyword-strategy"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export function SeoTemplate() {
  const [selectedCategory, setSelectedCategory] = useState<string>("AI Tools")
  const [selectedKeywords, setSelectedKeywords] = useState<string[]>([])

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category)
    setSelectedKeywords([])
  }

  const toggleKeyword = (keyword: string) => {
    if (selectedKeywords.includes(keyword)) {
      setSelectedKeywords(selectedKeywords.filter((k) => k !== keyword))
    } else {
      setSelectedKeywords([...selectedKeywords, keyword])
    }
  }

  const generateSeoTitle = () => {
    if (selectedKeywords.length === 0) return ""

    const templates = [
      `${selectedKeywords[0]}: Complete Guide for 2023`,
      `How to ${selectedKeywords[0]} (Step-by-Step Guide)`,
      `Top 10 Ways to ${selectedKeywords[0]} and Boost Your Income`,
      `The Ultimate Guide to ${selectedKeywords[0]} for Beginners`,
      `${selectedKeywords[0]}: Expert Tips and Strategies`,
    ]

    return templates[Math.floor(Math.random() * templates.length)]
  }

  const generateSeoDescription = () => {
    if (selectedKeywords.length === 0) return ""

    const templates = [
      `Learn how to ${selectedKeywords[0]} with our comprehensive guide. Discover proven strategies, tools, and tips to maximize your affiliate marketing success.`,
      `Looking to ${selectedKeywords[0]}? Our expert guide provides actionable insights and step-by-step instructions to help you succeed in affiliate marketing.`,
      `Discover the best strategies for ${selectedKeywords[0]} in our detailed guide. Increase your conversions and boost your affiliate income with these proven techniques.`,
    ]

    return templates[Math.floor(Math.random() * templates.length)].substring(0, 160)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>SEO Keyword Planner</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="high-intent">
          <TabsList className="mb-4">
            <TabsTrigger value="high-intent">High-Intent Keywords</TabsTrigger>
            <TabsTrigger value="long-tail">Long-Tail Keywords</TabsTrigger>
            <TabsTrigger value="category">Category Keywords</TabsTrigger>
          </TabsList>

          <TabsContent value="high-intent">
            <div className="space-y-4">
              <div className="flex flex-wrap gap-2">
                {keywordStrategy.highIntentKeywords.map((keyword, index) => (
                  <Button
                    key={index}
                    variant={selectedKeywords.includes(keyword) ? "default" : "outline"}
                    size="sm"
                    onClick={() => toggleKeyword(keyword)}
                  >
                    {keyword}
                  </Button>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="long-tail">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.entries(keywordStrategy.longTailKeywords).map(([category, keywords]) => (
                  <Card key={category}>
                    <CardHeader>
                      <CardTitle className="text-sm">{category}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-col gap-2">
                        {keywords.slice(0, 5).map((keyword, index) => (
                          <Button
                            key={index}
                            variant={selectedKeywords.includes(keyword) ? "default" : "outline"}
                            size="sm"
                            onClick={() => toggleKeyword(keyword)}
                          >
                            {keyword}
                          </Button>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="category">
            <div className="space-y-4">
              <div className="flex flex-wrap gap-2 mb-4">
                {Object.keys(keywordStrategy.categoryKeywords).map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleCategoryChange(category)}
                  >
                    {category}
                  </Button>
                ))}
              </div>

              <div className="flex flex-wrap gap-2">
                {keywordStrategy.categoryKeywords[
                  selectedCategory as keyof typeof keywordStrategy.categoryKeywords
                ]?.map((keyword, index) => (
                  <Button
                    key={index}
                    variant={selectedKeywords.includes(keyword) ? "default" : "outline"}
                    size="sm"
                    onClick={() => toggleKeyword(keyword)}
                  >
                    {keyword}
                  </Button>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-8 space-y-4">
          <h3 className="text-lg font-medium">Selected Keywords</h3>
          <div className="flex flex-wrap gap-2">
            {selectedKeywords.map((keyword, index) => (
              <div key={index} className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm">
                {keyword}
              </div>
            ))}
          </div>

          <div className="pt-4 border-t mt-6">
            <h3 className="text-lg font-medium mb-4">Generated SEO Elements</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">SEO Title</label>
                <Input value={generateSeoTitle()} readOnly />
                <p className="text-xs text-muted-foreground mt-1">Optimal length: 50-60 characters</p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Meta Description</label>
                <Textarea value={generateSeoDescription()} readOnly rows={3} />
                <p className="text-xs text-muted-foreground mt-1">Optimal length: 150-160 characters</p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Suggested URL Slug</label>
                <Input
                  value={selectedKeywords.length > 0 ? selectedKeywords[0].toLowerCase().replace(/\s+/g, "-") : ""}
                  readOnly
                />
              </div>
            </div>
          </div>

          <div className="pt-4 border-t mt-6">
            <h3 className="text-lg font-medium mb-2">SEO Implementation Tips</h3>
            <ul className="space-y-2 text-sm">
              {keywordStrategy.implementationTips.map((tip, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span>{tip}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default SeoTemplate
