import { createServerClient } from "@/lib/supabase/server"
import { getServerSideSitemap } from "next-sitemap"

export async function GET(request: Request): Promise<Response> {
  try {
    const supabase = createServerClient()

    // Fetch all published blog posts
    const { data: posts } = await supabase
      .from("blog_posts")
      .select("slug, updated_at, published_at")
      .eq("published", true)

    // Create sitemap entries for blog posts
    const blogEntries =
      posts?.map((post) => ({
        loc: `https://earnifyai.com/blog/${post.slug}`,
        lastmod: new Date(post.updated_at || post.published_at).toISOString(),
        changefreq: "weekly",
        priority: 0.8,
      })) || []

    // Add other important pages
    const staticPages = [
      {
        loc: "https://earnifyai.com",
        lastmod: new Date().toISOString(),
        changefreq: "daily",
        priority: 1.0,
      },
      {
        loc: "https://earnifyai.com/about",
        lastmod: new Date().toISOString(),
        changefreq: "monthly",
        priority: 0.8,
      },
      {
        loc: "https://earnifyai.com/blog",
        lastmod: new Date().toISOString(),
        changefreq: "daily",
        priority: 0.9,
      },
      {
        loc: "https://earnifyai.com/contact",
        lastmod: new Date().toISOString(),
        changefreq: "monthly",
        priority: 0.7,
      },
      {
        loc: "https://earnifyai.com/affiliate-disclosure",
        lastmod: new Date().toISOString(),
        changefreq: "monthly",
        priority: 0.6,
      },
    ]

    // Combine all entries
    const allEntries = [...staticPages, ...blogEntries]

    return getServerSideSitemap(allEntries)
  } catch (error) {
    console.error("Error generating sitemap:", error)
    return new Response("Error generating sitemap", { status: 500 })
  }
}
