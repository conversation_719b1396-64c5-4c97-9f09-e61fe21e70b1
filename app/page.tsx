import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardD<PERSON>cription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON>R<PERSON>, CheckCircle, TrendingUp, Zap, DollarSign, <PERSON> } from "lucide-react"
import { ParticlesBackground } from "@/components/ui-enhancements/particles-background"
import { ModernCard } from "@/components/ui-enhancements/modern-card"
import { GradientButton } from "@/components/ui-enhancements/gradient-button"

const features = [
  {
    name: "AI-Powered Tools",
    description: "Discover cutting-edge AI tools that automate your workflow and boost your productivity.",
    icon: Brain,
  },
  {
    name: "Affiliate Strategies",
    description: "Learn proven affiliate marketing strategies to generate passive income streams.",
    icon: TrendingUp,
  },
  {
    name: "Quick Implementation",
    description: "Get started quickly with step-by-step guides and ready-to-use templates.",
    icon: Zap,
  },
  {
    name: "Income Opportunities",
    description: "Explore multiple digital income opportunities beyond traditional affiliate marketing.",
    icon: DollarSign,
  },
]

const tools = [
  {
    name: "ContentGenius AI",
    description: "AI-powered content creation tool that generates high-converting copy for your affiliate sites.",
    image: "https://images.unsplash.com/photo-1655720828018-edd2daec9349?q=80&w=600&auto=format&fit=crop",
    category: "Content Creation",
  },
  {
    name: "TrafficBoost Pro",
    description: "Advanced SEO tool that helps you drive targeted traffic to your affiliate offers.",
    image: "https://images.unsplash.com/photo-1533750516457-a7f992034fec?q=80&w=600&auto=format&fit=crop",
    category: "SEO",
  },
  {
    name: "ConversionMaster",
    description: "Optimize your landing pages and boost conversion rates with this AI-powered tool.",
    image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=600&auto=format&fit=crop",
    category: "Conversion Optimization",
  },
]

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-50/80 to-cyan-50/80 dark:from-purple-950/20 dark:to-cyan-950/20 -z-10" />
        <ParticlesBackground />
        <div className="container px-4 md:px-6 mx-auto">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
            <div className="flex flex-col justify-center space-y-4">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none animate-fade-in">
                  Discover AI Tools & Smart Strategies to
                  <span className="bg-gradient-to-r from-purple-600 to-cyan-500 bg-clip-text text-transparent">
                    {" "}
                    Make Money Online
                  </span>
                </h1>
                <p className="max-w-[600px] text-muted-foreground md:text-xl animate-fade-in-delay">
                  Learn affiliate marketing, productivity hacks, and digital income tips — all in one place.
                </p>
              </div>
              <div className="flex flex-col gap-2 min-[400px]:flex-row animate-fade-in-delay-2">
                <Link href="/blog">
                  <GradientButton size="lg">
                    Explore AI Tools
                  </GradientButton>
                </Link>
                <Link href="/about">
                  <Button size="lg" variant="outline" className="transition-all duration-300 hover:bg-primary/10">
                    Learn More
                  </Button>
                </Link>
              </div>
            </div>
            <div className="mx-auto lg:mx-0 relative animate-fade-in-right">
              <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-600 to-cyan-500 rounded-lg blur opacity-30 group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-pulse"></div>
              <Image
                src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=500&auto=format&fit=crop"
                alt="EarnifyAI Dashboard"
                width={500}
                height={500}
                className="rounded-lg shadow-xl relative"
                priority
              />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Why Choose EarnifyAI</h2>
            <p className="mt-4 text-xl text-muted-foreground">
              We help you navigate the world of AI-powered affiliate marketing
            </p>
          </div>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
            {features.map((feature, index) => (
              <ModernCard
                key={feature.name}
                className="border-0 shadow-md hover:shadow-lg transition-shadow"
                hoverEffect="lift"
              >
                <CardHeader>
                  <div className="rounded-full w-12 h-12 flex items-center justify-center bg-primary/10 mb-4">
                    <feature.icon className="h-6 w-6 text-purple-600" />
                  </div>
                  <CardTitle>{feature.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{feature.description}</CardDescription>
                </CardContent>
              </ModernCard>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Tools Section */}
      <section className="py-16 md:py-24 bg-muted/50">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Featured AI Tools</h2>
            <p className="mt-4 text-xl text-muted-foreground">
              Discover the best AI tools to boost your affiliate marketing success
            </p>
          </div>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {tools.map((tool) => (
              <ModernCard key={tool.name} className="overflow-hidden" hoverEffect="glow">
                <div className="aspect-video relative overflow-hidden group">
                  <Image
                    src={tool.image || "/placeholder.svg"}
                    alt={tool.name}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute top-2 right-2 bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-medium">
                    {tool.category}
                  </div>
                </div>
                <CardHeader>
                  <CardTitle>{tool.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{tool.description}</CardDescription>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full group transition-all duration-300 hover:bg-primary/10">
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                  </Button>
                </CardFooter>
              </ModernCard>
            ))}
          </div>
          <div className="text-center mt-12">
            <Link href="/blog">
              <GradientButton size="lg">
                View All AI Tools
              </GradientButton>
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Success Stories</h2>
            <p className="mt-4 text-xl text-muted-foreground">
              See how others have transformed their online income with our strategies
            </p>
          </div>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {[
              {
                name: "John Davis",
                role: "Affiliate Marketer",
                testimonial:
                  "EarnifyAI's strategies helped me 3x my affiliate income in just 2 months. The AI tool recommendations were game-changing for my business.",
                image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=64&auto=format&fit=crop",
              },
              {
                name: "Sarah Johnson",
                role: "Content Creator",
                testimonial:
                  "The AI tools recommended by EarnifyAI have cut my content creation time in half while improving quality. My conversion rates have never been better!",
                image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=64&auto=format&fit=crop",
              },
              {
                name: "Michael Chen",
                role: "Digital Entrepreneur",
                testimonial:
                  "I've tried many affiliate marketing resources, but EarnifyAI's approach to combining AI with proven strategies has been the most effective by far.",
                image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=64&auto=format&fit=crop",
              },
            ].map((testimonial, i) => (
              <Card key={i} className="text-center">
                <CardHeader>
                  <div className="flex justify-center">
                    <div className="relative h-16 w-16 rounded-full overflow-hidden">
                      <Image
                        src={testimonial.image || "/placeholder.svg"}
                        alt={`${testimonial.name} testimonial`}
                        fill
                        className="object-cover"
                      />
                    </div>
                  </div>
                  <CardTitle className="mt-4">{testimonial.name}</CardTitle>
                  <CardDescription>{testimonial.role}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">"{testimonial.testimonial}"</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-gradient-to-r from-purple-600 to-cyan-500 text-white">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="grid gap-6 lg:grid-cols-2 items-center">
            <div>
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Ready to Transform Your Online Income?
              </h2>
              <ul className="mt-8 space-y-4">
                {[
                  "Access to premium AI tool reviews",
                  "Step-by-step affiliate marketing guides",
                  "Exclusive income strategies",
                  "Community support",
                ].map((item) => (
                  <li key={item} className="flex items-center">
                    <CheckCircle className="h-6 w-6 mr-2 flex-shrink-0" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="flex justify-center lg:justify-end">
              <div className="w-full max-w-md bg-white text-foreground rounded-lg shadow-xl p-8">
                <h3 className="text-2xl font-bold mb-4">Join Our Newsletter</h3>
                <p className="text-muted-foreground mb-6">
                  Get the latest AI tools and affiliate strategies delivered to your inbox.
                </p>
                <form className="space-y-4">
                  <div>
                    <input
                      type="email"
                      placeholder="Your email address"
                      className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      required
                    />
                  </div>
                  <Button className="w-full bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600">
                    Subscribe Now
                  </Button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
