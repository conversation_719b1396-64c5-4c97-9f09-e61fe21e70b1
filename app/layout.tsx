import type React from "react"
import { Inter } from "next/font/google"
import { ThemeProvider } from "@/components/theme-provider"
import { ConditionalLayout } from "@/components/conditional-layout"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export const metadata = {
  title: {
    default: "EarnifyAI | AI Tools & Affiliate Marketing Strategies",
    template: "%s | EarnifyAI",
  },
  description:
    "Discover the best AI tools and smart strategies to make money online. Learn affiliate marketing, productivity hacks, and digital income tips — all in one place.",
  keywords: ["affiliate marketing", "AI tools", "make money online", "digital income", "productivity hacks"],
  icons: {
    icon: [
      { url: '/favicon.svg' },
    ],
    shortcut: '/favicon.svg',
    apple: '/favicon.svg',
  },
  openGraph: {
    title: "EarnifyAI | AI Tools & Affiliate Marketing Strategies",
    description: "Discover the best AI tools and smart strategies to make money online.",
    url: "https://earnifyai.com",
    siteName: "EarnifyAI",
    locale: "en_US",
    type: "website",
  },
  robots: {
    index: true,
    follow: true,
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <ConditionalLayout>{children}</ConditionalLayout>
        </ThemeProvider>
      </body>
    </html>
  )
}
