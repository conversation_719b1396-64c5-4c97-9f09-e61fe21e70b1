import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Home, Search } from "lucide-react"
import type { Metada<PERSON> } from "next"

export const metadata: Metadata = {
  title: "Page Not Found | EarnifyAI",
  description: "The page you're looking for doesn't exist. Navigate back to our homepage or explore our blog.",
  robots: {
    index: false,
    follow: true,
  },
}

export default function NotFound() {
  return (
    <div className="flex flex-col min-h-screen items-center justify-center p-4">
      <div className="text-center max-w-md">
        <h1 className="text-4xl font-bold mb-4">404 - Page Not Found</h1>
        <p className="text-muted-foreground mb-8">The page you're looking for doesn't exist or has been moved.</p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/">
            <Button className="w-full sm:w-auto">
              <Home className="mr-2 h-4 w-4" />
              Back to Home
            </Button>
          </Link>
          <Link href="/blog">
            <Button variant="outline" className="w-full sm:w-auto">
              <Search className="mr-2 h-4 w-4" />
              Explore Blog
            </Button>
          </Link>
        </div>

        <div className="mt-12">
          <h2 className="text-xl font-semibold mb-4">Popular Content</h2>
          <ul className="space-y-2">
            <li>
              <Link href="/blog/getting-started-with-ai-tools" className="text-primary hover:underline">
                Getting Started with AI Tools for Affiliate Marketing
              </Link>
            </li>
            <li>
              <Link href="/blog/top-seo-strategies-2023" className="text-primary hover:underline">
                Top 10 SEO Strategies for 2023
              </Link>
            </li>
            <li>
              <Link href="/about" className="text-primary hover:underline">
                About EarnifyAI
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </div>
  )
}
