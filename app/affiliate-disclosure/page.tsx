import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { AlertTriangle, Info } from "lucide-react"

export const metadata = {
  title: "Affiliate Disclosure",
  description: "EarnifyAI affiliate disclosure and transparency statement.",
}

export default function AffiliateDisclosurePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative py-14 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-cyan-50 dark:from-purple-950/20 dark:to-cyan-950/20 -z-10" />
        <div className="container px-4 md:px-6 mx-auto">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none mb-6">
              Affiliate{" "}
              <span className="bg-gradient-to-r from-purple-600 to-cyan-500 bg-clip-text text-transparent">
                Disclosure
              </span>
            </h1>
            <p className="text-muted-foreground md:text-xl mb-8">
              Transparency is one of our core values. Here's how we make money.
            </p>
          </div>
        </div>
      </section>

      {/* Disclosure Content */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="max-w-3xl mx-auto">
            <Card className="mb-8">
              <CardHeader className="flex flex-row items-center gap-4">
                <Info className="h-8 w-8 text-purple-600" />
                <div>
                  <CardTitle>Our Commitment to Transparency</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-muted-foreground text-base space-y-4">
                  <p>
                    At EarnifyAI, we believe in complete transparency about how we make money. This page outlines our
                    affiliate relationships and how they might influence the content on our website.
                  </p>
                  <p>
                    We want you to make informed decisions about the tools and services we recommend, with a clear
                    understanding of our business model.
                  </p>
                </div>
              </CardContent>
            </Card>

            <div className="prose prose-purple dark:prose-invert max-w-none">
              <h2 className="text-2xl font-bold tracking-tight mb-4">What Are Affiliate Links?</h2>
              <p className="text-muted-foreground mb-6">
                Affiliate links are special URLs that contain a tracking code. When you click on an affiliate link and
                make a purchase or sign up for a service, we may earn a commission at no additional cost to you.
              </p>

              <h2 className="text-2xl font-bold tracking-tight mb-4 mt-8">How We Use Affiliate Links</h2>
              <p className="text-muted-foreground mb-6">
                Many of the AI tools, products, and services we recommend on EarnifyAI are part of affiliate programs.
                This means we may earn a commission if you click on our links and make a purchase.
              </p>
              <p className="text-muted-foreground mb-6">We use these affiliate links in:</p>
              <ul className="list-disc pl-6 text-muted-foreground mb-6">
                <li>Tool reviews and comparisons</li>
                <li>Tutorial content</li>
                <li>Resource recommendations</li>
                <li>Case studies</li>
                <li>Email newsletters</li>
              </ul>

              <h2 className="text-2xl font-bold tracking-tight mb-4 mt-8">Our Review Process</h2>
              <p className="text-muted-foreground mb-6">
                We want to emphasize that our affiliate relationships do NOT influence our reviews or recommendations.
                Our evaluation process includes:
              </p>
              <ul className="list-disc pl-6 text-muted-foreground mb-6">
                <li>Hands-on testing of each tool or service</li>
                <li>Evaluation based on predefined criteria</li>
                <li>Consideration of user feedback and reviews</li>
                <li>Regular re-evaluation of our recommendations</li>
              </ul>
              <p className="text-muted-foreground mb-6">
                We only recommend products we genuinely believe will benefit our audience, regardless of whether we have
                an affiliate relationship with the company.
              </p>

              <div className="bg-muted p-6 rounded-lg my-8">
                <div className="flex items-start">
                  <AlertTriangle className="h-6 w-6 text-amber-500 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium mb-2">Important Note</h3>
                    <p className="text-muted-foreground">
                      While we do our best to keep all information on EarnifyAI up-to-date, pricing, features, and terms
                      of the products and services we review may change over time. Always check the official website of
                      any product or service before making a purchase.
                    </p>
                  </div>
                </div>
              </div>

              <h2 className="text-2xl font-bold tracking-tight mb-4 mt-8">Our Affiliate Partners</h2>
              <p className="text-muted-foreground mb-6">
                We currently have affiliate relationships with the following companies:
              </p>
              <ul className="list-disc pl-6 text-muted-foreground mb-6">
                <li>ContentGenius AI</li>
                <li>TrafficBoost Pro</li>
                <li>ConversionMaster</li>
                <li>AIWriter.com</li>
                <li>SEORank</li>
                <li>EmailAI</li>
                <li>And various other AI tool providers</li>
              </ul>
              <p className="text-muted-foreground mb-6">
                This list is regularly updated as we form new partnerships or end existing ones.
              </p>

              <h2 className="text-2xl font-bold tracking-tight mb-4 mt-8">
                Questions About Our Affiliate Relationships
              </h2>
              <p className="text-muted-foreground mb-6">
                If you have any questions about our affiliate relationships or how we make money, please don't hesitate
                to{" "}
                <a href="/contact" className="text-purple-600 hover:underline">
                  contact us
                </a>
                . We're committed to transparency and are happy to provide additional information.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
