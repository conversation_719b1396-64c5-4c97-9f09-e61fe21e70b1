"use client"
import { useParams } from "next/navigation"
import { BlogSchema } from "@/components/seo/blog-schema"

// Import other components and types as needed

export default function BlogPostClient({ post, imageUrl, relatedPosts }) {
  const params = useParams()
  const slug = params.slug as string

  // Rest of your component logic

  return (
    <>
      {/* Add the BlogSchema component */}
      <BlogSchema
        title={post.title}
        description={post.excerpt || `Read about ${post.title} on EarnifyAI`}
        datePublished={post.published_at || post.created_at}
        dateModified={post.updated_at}
        authorName={post.admin_users?.full_name || "EarnifyAI Team"}
        imageUrl={imageUrl}
        url={`https://earnifyai.com/blog/${slug}`}
        keywords={[post.category, "AI Tools", "Affiliate Marketing", "Make Money Online"]}
      />

      {/* Rest of your component JSX */}
    </>
  )
}
