import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"

export default function BlogPostNotFound() {
  return (
    <div className="flex flex-col min-h-screen items-center justify-center p-4">
      <div className="text-center max-w-md">
        <h1 className="text-4xl font-bold mb-4">Post Not Found</h1>
        <p className="text-muted-foreground mb-8">
          The blog post you're looking for doesn't exist or has been removed.
        </p>
        <Link href="/blog">
          <Button>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Blog
          </Button>
        </Link>
      </div>
    </div>
  )
}
