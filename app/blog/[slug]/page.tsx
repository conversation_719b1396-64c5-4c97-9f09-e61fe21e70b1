import type { <PERSON>ada<PERSON> } from "next"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { createServerClient } from "@/lib/supabase/server"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ArrowLeft, Calendar, Clock, Tag, User } from "lucide-react"
import { format } from "date-fns"
import { PostNavigation } from "@/components/post-navigation"
// Import the ScrollProgress component
import { ScrollProgress } from "@/components/scroll-progress"

type BlogPostPageProps = {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const supabase = createServerClient()
  const { data: post } = await supabase
    .from("blog_posts")
    .select("title, excerpt, featured_image, category, content, published_at, created_at, updated_at")
    .eq("slug", params.slug)
    .eq("published", true)
    .single()

  if (!post) {
    return {
      title: "Post Not Found",
      description: "The requested blog post could not be found.",
    }
  }

  // Extract keywords from the post category and title
  const keywords = [
    post.category || "Affiliate Marketing",
    "AI Tools",
    "Affiliate Marketing",
    ...post.title
      .split(" ")
      .filter((word) => word.length > 3)
      .slice(0, 5),
  ]

  // Create a description that's SEO-friendly
  const description =
    post.excerpt ||
    `Learn about ${post.title} in our comprehensive guide. Discover how to leverage AI tools for affiliate marketing and boost your online income.`

  return {
    title: `${post.title} | EarnifyAI`,
    description: description.substring(0, 160), // Keep description under 160 characters
    keywords: keywords,
    authors: [{ name: "EarnifyAI Team" }],
    openGraph: {
      title: post.title,
      description: description.substring(0, 160),
      type: "article",
      url: `https://earnifyai.com/blog/${params.slug}`,
      images: post.featured_image ? [{ url: post.featured_image }] : undefined,
      publishedTime: post.published_at,
      modifiedTime: post.updated_at || post.published_at,
      authors: ["EarnifyAI Team"],
      tags: keywords,
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: description.substring(0, 160),
      images: post.featured_image ? [post.featured_image] : undefined,
    },
    alternates: {
      canonical: `https://earnifyai.com/blog/${params.slug}`,
    },
  }
}

// Function to get a fallback image if the featured image is missing or invalid
const getImageUrl = (post: any) => {
  if (!post.featured_image) {
    // Return a category-specific fallback image
    switch (post.category) {
      case "AI Tools":
        return "https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=1200&auto=format&fit=crop"
      case "SEO":
        return "https://images.unsplash.com/photo-1562577309-4932fdd64cd1?q=80&w=1200&auto=format&fit=crop"
      case "Guides":
        return "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=1200&auto=format&fit=crop"
      case "Case Studies":
        return "https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?q=80&w=1200&auto=format&fit=crop"
      case "Ethics":
        return "https://images.unsplash.com/photo-1589829545856-d10d557cf95f?q=80&w=1200&auto=format&fit=crop"
      case "Productivity":
        return "https://images.unsplash.com/photo-1483058712412-4245e9b90334?q=80&w=1200&auto=format&fit=crop"
      case "Income Strategies":
        return "https://images.unsplash.com/photo-1579621970795-87facc2f976d?q=80&w=1200&auto=format&fit=crop"
      case "Reviews":
        return "https://images.unsplash.com/photo-1519389950473-47ba0277781c?q=80&w=1200&auto=format&fit=crop"
      default:
        return "https://images.unsplash.com/photo-1661956602944-249bcd04b63f?q=80&w=1200&auto=format&fit=crop"
    }
  }
  return post.featured_image
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const supabase = createServerClient()
  const { data: post } = await supabase
    .from("blog_posts")
    .select("*, admin_users(full_name)")
    .eq("slug", params.slug)
    .eq("published", true)
    .single()

  if (!post) {
    notFound()
  }

  // Get related posts in the same category
  const { data: relatedPosts } = await supabase
    .from("blog_posts")
    .select("title, slug, featured_image, category, published_at, created_at")
    .eq("category", post.category)
    .eq("published", true)
    .neq("id", post.id)
    .limit(3)

  // Calculate read time (rough estimate based on word count)
  const wordCount = post.content ? post.content.split(/\s+/).length : 0
  const readTime = Math.ceil(wordCount / 200) // Assuming 200 words per minute reading speed

  // Format the content for display
  // This handles both plain text and HTML content
  const renderContent = () => {
    if (!post.content) return <p>No content available.</p>

    // Check if content appears to be HTML
    if (post.content.includes("<")) {
      return <div dangerouslySetInnerHTML={{ __html: post.content }} />
    }

    // Otherwise, treat as plain text with paragraphs
    return post.content
      .split("\n")
      .map((paragraph: string, index: number) =>
        paragraph.trim() ? (
          <p key={index} className="mb-4">
            {paragraph}
          </p>
        ) : null,
      )
      .filter(Boolean)
  }

  // Add it at the top of the component return
  return (
    <div className="flex flex-col min-h-screen">
      <ScrollProgress />
      {/* Rest of the component */}
      {/* Hero Section */}
      <section className="relative py-12 md:py-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-cyan-50 dark:from-purple-950/20 dark:to-cyan-950/20 -z-10" />
        <div className="container px-4 md:px-6 mx-auto">
          <div className="max-w-3xl mx-auto">
            <Link
              href="/blog"
              className="inline-flex items-center text-sm font-medium text-muted-foreground mb-6 hover:text-primary"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Blog
            </Link>
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl mb-6">{post.title}</h1>
            <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-8">
              {post.admin_users?.full_name && (
                <div className="flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  <span>{post.admin_users.full_name}</span>
                </div>
              )}
              <div className="flex items-center">
                <Calendar className="mr-2 h-4 w-4" />
                <span>{format(new Date(post.published_at || post.created_at), "MMMM d, yyyy")}</span>
              </div>
              {post.category && (
                <div className="flex items-center">
                  <Tag className="mr-2 h-4 w-4" />
                  <span>{post.category}</span>
                </div>
              )}
              <div className="flex items-center">
                <Clock className="mr-2 h-4 w-4" />
                <span>{readTime} min read</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Image */}
      <section className="py-4">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="relative aspect-video rounded-lg overflow-hidden">
              <Image
                src={getImageUrl(post) || "/placeholder.svg"}
                alt={post.title}
                fill
                className="object-cover"
                priority
              />
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-8 md:py-12">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              <div className="max-w-3xl mx-auto">
                {post.excerpt && (
                  <div className="mb-8 text-xl text-muted-foreground italic border-l-4 border-primary pl-4 py-2">
                    {post.excerpt}
                  </div>
                )}
                <article className="prose prose-purple dark:prose-invert max-w-none prose-headings:font-bold prose-headings:tracking-tight prose-p:leading-relaxed prose-img:rounded-lg">
                  {renderContent()}
                </article>

                {/* Tags Section */}
                {post.category && (
                  <div className="mt-12 pt-6 border-t">
                    <div className="flex items-center flex-wrap gap-2">
                      <span className="text-sm font-medium">Tags:</span>
                      <Link href={`/blog?category=${encodeURIComponent(post.category)}`}>
                        <Button variant="outline" size="sm" className="rounded-full">
                          {post.category}
                        </Button>
                      </Link>
                    </div>
                  </div>
                )}

                {/* Author Section */}
                {post.admin_users?.full_name && (
                  <div className="mt-12 pt-6 border-t">
                    <div className="flex items-center gap-4">
                      <div className="relative h-12 w-12 rounded-full overflow-hidden bg-muted">
                        <div className="absolute inset-0 flex items-center justify-center text-lg font-bold">
                          {post.admin_users.full_name.charAt(0)}
                        </div>
                      </div>
                      <div>
                        <h3 className="font-medium">Written by {post.admin_users.full_name}</h3>
                        <p className="text-sm text-muted-foreground">
                          Published on {format(new Date(post.published_at || post.created_at), "MMMM d, yyyy")}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="lg:col-span-1">
              <div className="sticky top-24">
                <h3 className="text-lg font-semibold mb-4">Related Posts</h3>
                <div className="space-y-4">
                  {relatedPosts?.length ? (
                    relatedPosts.map((relatedPost) => (
                      <Link key={relatedPost.slug} href={`/blog/${relatedPost.slug}`}>
                        <Card className="overflow-hidden hover:shadow-md transition-shadow">
                          <div className="relative aspect-video">
                            <Image
                              src={relatedPost.featured_image || getImageUrl({ category: relatedPost.category })}
                              alt={relatedPost.title}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <CardContent className="p-4">
                            <h4 className="font-medium line-clamp-2">{relatedPost.title}</h4>
                            <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                              {relatedPost.category && <span>{relatedPost.category}</span>}
                              <span>•</span>
                              <span>
                                {format(new Date(relatedPost.published_at || relatedPost.created_at), "MMM d, yyyy")}
                              </span>
                            </div>
                          </CardContent>
                        </Card>
                      </Link>
                    ))
                  ) : (
                    <p className="text-muted-foreground">No related posts found</p>
                  )}
                </div>

                {/* Table of Contents */}
                <div className="mt-8 pt-8 border-t">
                  <h3 className="text-lg font-semibold mb-4">Table of Contents</h3>
                  <nav className="space-y-1">
                    {/* This is a simplified TOC - in a real app, you'd parse the content for headings */}
                    <a href="#introduction" className="block text-sm hover:text-primary">
                      Introduction
                    </a>
                    <a href="#main-content" className="block text-sm hover:text-primary">
                      Main Content
                    </a>
                    <a href="#conclusion" className="block text-sm hover:text-primary">
                      Conclusion
                    </a>
                  </nav>
                </div>

                {/* Share Buttons */}
                <div className="mt-8 pt-8 border-t">
                  <h3 className="text-lg font-semibold mb-4">Share This Post</h3>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="w-full">
                      Twitter
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      Facebook
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 bg-muted/50">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-2xl font-bold mb-4">Enjoyed this article?</h2>
            <p className="text-muted-foreground mb-6">
              Subscribe to our newsletter to get the latest AI tools and affiliate marketing strategies.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/blog">
                <Button variant="outline">Read More Articles</Button>
              </Link>
              <Link href="/#newsletter">
                <Button className="bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600">
                  Subscribe to Newsletter
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Next/Previous Posts */}
      <section className="py-12">
        <div className="container px-4 md:px-6 mx-auto">
          <h3 className="text-lg font-semibold mb-4">Continue Reading</h3>
          <PostNavigation currentPostId={post.id} />
        </div>
      </section>
    </div>
  )
}
