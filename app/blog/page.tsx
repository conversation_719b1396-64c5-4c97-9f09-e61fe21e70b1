import { Suspense } from "react"
import { Loader2 } from "lucide-react"
import BlogClient from "./page-client"

export const metadata = {
  title: "Blog - AI Tools & Affiliate Marketing Strategies",
  description: "Discover the latest AI tools, affiliate marketing strategies, and tips to boost your online income.",
}

export default function BlogPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <Suspense
        fallback={
          <div className="flex justify-center items-center min-h-screen">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        }
      >
        <BlogClient />
      </Suspense>
    </div>
  )
}
