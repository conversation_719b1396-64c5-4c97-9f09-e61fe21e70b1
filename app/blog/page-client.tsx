"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Search } from "@/components/search"
import { ArrowRight, Tag, Loader2, AlertCircle } from "lucide-react"
import { format } from "date-fns"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
// Import the new BlogCard component
import { BlogCard } from "@/components/blog-card"

type BlogPost = {
  id: string
  title: string
  slug: string
  excerpt: string | null
  content: string | null
  featured_image: string | null
  category: string | null
  published_at: string | null
  created_at: string
  admin_users: {
    full_name: string | null
  } | null
}

// Sample fallback data in case the API fails
const samplePosts: BlogPost[] = [
  {
    id: "1",
    title: "Getting Started with AI Tools for Affiliate Marketing",
    slug: "getting-started-with-ai-tools",
    excerpt: "Learn how to leverage AI tools to boost your affiliate marketing strategy.",
    content: "This is a sample post content.",
    featured_image: "https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=1200&auto=format&fit=crop",
    category: "AI Tools",
    published_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    admin_users: {
      full_name: "Admin User",
    },
  },
  {
    id: "2",
    title: "Top 10 SEO Strategies for 2023",
    slug: "top-seo-strategies-2023",
    excerpt: "Discover the most effective SEO strategies to improve your website ranking.",
    content: "This is a sample post content.",
    featured_image: "https://images.unsplash.com/photo-1562577309-4932fdd64cd1?q=80&w=1200&auto=format&fit=crop",
    category: "SEO",
    published_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    admin_users: {
      full_name: "Admin User",
    },
  },
]

export default function BlogClient() {
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [featuredPost, setFeaturedPost] = useState<BlogPost | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const searchParams = useSearchParams()
  const category = searchParams.get("category")
  const search = searchParams.get("search")

  useEffect(() => {
    const fetchPosts = async () => {
      setLoading(true)
      setError(null)

      try {
        let url = `/api/blog?page=${page}&limit=6`
        if (category) url += `&category=${encodeURIComponent(category)}`
        if (search) url += `&search=${encodeURIComponent(search)}`

        const response = await fetch(url)

        if (!response.ok) {
          throw new Error(`API responded with status: ${response.status}`)
        }

        const data = await response.json()

        if (data.error) {
          throw new Error(data.error)
        }

        // Check if we have posts
        if (!data.posts || data.posts.length === 0) {
          // If we're on the first page and there's no filtering, use sample data
          if (page === 1 && !category && !search) {
            console.log("Using sample data as fallback")
            setPosts(samplePosts)
            setFeaturedPost(samplePosts[0])
            setTotalPages(1)
          } else {
            setPosts([])
            setFeaturedPost(null)
            setTotalPages(data.totalPages || 1)
          }
        } else {
          console.log(`Received ${data.posts.length} posts from API`)
          setPosts(data.posts)
          setTotalPages(data.totalPages || 1)

          // Set featured post if on first page and not filtering
          if (page === 1 && !category && !search && data.posts.length > 0) {
            setFeaturedPost(data.posts[0])
          } else {
            setFeaturedPost(null)
          }
        }
      } catch (err) {
        console.error("Error fetching posts:", err)
        setError(err instanceof Error ? err.message : "Failed to fetch posts")

        // Use sample data as fallback
        if (page === 1 && !category && !search) {
          setPosts(samplePosts)
          setFeaturedPost(samplePosts[0])
          setTotalPages(1)
        }
      } finally {
        setLoading(false)
      }
    }

    fetchPosts()
  }, [page, category, search])

  const calculateReadTime = (content: string) => {
    if (!content) return 1
    const wordCount = content.split(/\s+/).length
    return Math.ceil(wordCount / 200) // Assuming 200 words per minute reading speed
  }

  // Function to get a fallback image if the featured image is missing or invalid
  const getImageUrl = (post: BlogPost) => {
    if (!post.featured_image) {
      // Return a category-specific fallback image
      switch (post.category) {
        case "AI Tools":
          return "https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=600&auto=format&fit=crop"
        case "SEO":
          return "https://images.unsplash.com/photo-1562577309-4932fdd64cd1?q=80&w=600&auto=format&fit=crop"
        case "Guides":
          return "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=600&auto=format&fit=crop"
        case "Case Studies":
          return "https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?q=80&w=600&auto=format&fit=crop"
        case "Ethics":
          return "https://images.unsplash.com/photo-1589829545856-d10d557cf95f?q=80&w=600&auto=format&fit=crop"
        case "Productivity":
          return "https://images.unsplash.com/photo-1483058712412-4245e9b90334?q=80&w=600&auto=format&fit=crop"
        case "Income Strategies":
          return "https://images.unsplash.com/photo-1579621970795-87facc2f976d?q=80&w=600&auto=format&fit=crop"
        case "Reviews":
          return "https://images.unsplash.com/photo-1519389950473-47ba0277781c?q=80&w=600&auto=format&fit=crop"
        default:
          return "https://images.unsplash.com/photo-1661956602944-249bcd04b63f?q=80&w=600&auto=format&fit=crop"
      }
    }
    return post.featured_image
  }

  return (
    <>
      {/* Hero Section */}
      <section className="relative py-14 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-cyan-50 dark:from-purple-950/20 dark:to-cyan-950/20 -z-10" />
        <div className="container px-4 md:px-6 mx-auto">
          <div className="text-center mx-auto">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none mb-6">
              EarnifyAI{" "}
              <span className="bg-gradient-to-r from-purple-600 to-cyan-500 bg-clip-text text-transparent">Blog</span>
            </h1>
            <p className="text-muted-foreground md:text-xl mb-8">
              Discover the latest AI tools, affiliate marketing strategies, and tips to boost your online income.
            </p>
            <div className="flex justify-center">
              <Search />
            </div>
          </div>
        </div>
      </section>

      {/* Error Alert */}
      {error && (
        <div className="container px-4 md:px-6 mx-auto py-4">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}. Showing sample posts instead.</AlertDescription>
          </Alert>
        </div>
      )}

      {/* Featured Post */}
      {featuredPost && (
        <section className="py-12 md:py-16">
          <div className="container px-4 md:px-6 mx-auto">
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
              <div className="relative aspect-video overflow-hidden rounded-lg">
                <Image
                  src={getImageUrl(featuredPost) || "/placeholder.svg"}
                  alt={featuredPost.title}
                  fill
                  className="object-cover"
                />
                <div className="absolute top-4 left-4 bg-primary text-primary-foreground px-3 py-1 rounded-full text-sm font-medium">
                  Featured
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                    {featuredPost.category && <span>{featuredPost.category}</span>}
                    <span>•</span>
                    <span>
                      {format(new Date(featuredPost.published_at || featuredPost.created_at), "MMMM d, yyyy")}
                    </span>
                    <span>•</span>
                    <span>{calculateReadTime(featuredPost.content || "")} min read</span>
                  </div>
                  <h2 className="text-3xl font-bold tracking-tight">{featuredPost.title}</h2>
                </div>
                <p className="text-muted-foreground">{featuredPost.excerpt}</p>
                <div>
                  <Link href={`/blog/${featuredPost.slug}`}>
                    <Button>
                      Read Full Article
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Blog Posts */}
      <section className="py-12 md:py-16">
        <div className="container px-4 md:px-6 mx-auto">
          {search && (
            <h2 className="text-2xl font-bold mb-8">
              Search results for: <span className="text-primary">"{search}"</span>
            </h2>
          )}

          {category && (
            <h2 className="text-2xl font-bold mb-8">
              Category: <span className="text-primary">{category}</span>
            </h2>
          )}

          {loading ? (
            <div className="flex justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : posts.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-xl text-muted-foreground mb-4">No posts found</p>
              <p className="text-muted-foreground mb-6">
                {category
                  ? `No posts found in the "${category}" category.`
                  : search
                    ? `No posts match your search for "${search}".`
                    : "There are no blog posts available at the moment."}
              </p>
              <Link href="/blog">
                <Button variant="outline" className="mt-4">
                  View All Posts
                </Button>
              </Link>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {posts.map((post, index) => (
                  <BlogCard
                    key={post.id || index}
                    post={post}
                    imageUrl={getImageUrl(post)}
                    calculateReadTime={calculateReadTime}
                  />
                ))}
              </div>

              {totalPages > 1 && (
                <div className="mt-12 flex justify-center gap-2">
                  <Button variant="outline" onClick={() => setPage((p) => Math.max(p - 1, 1))} disabled={page === 1}>
                    Previous
                  </Button>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((p) => (
                    <Button key={p} variant={p === page ? "default" : "outline"} onClick={() => setPage(p)}>
                      {p}
                    </Button>
                  ))}
                  <Button
                    variant="outline"
                    onClick={() => setPage((p) => Math.min(p + 1, totalPages))}
                    disabled={page === totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-12 md:py-16 bg-muted/50">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold tracking-tight">Browse by Category</h2>
          </div>
          <div className="flex flex-wrap justify-center gap-4">
            {[
              "AI Tools",
              "SEO",
              "Guides",
              "Case Studies",
              "Ethics",
              "Productivity",
              "Income Strategies",
              "Reviews",
            ].map((cat) => (
              <Link key={cat} href={`/blog?category=${encodeURIComponent(cat)}`}>
                <Button variant="outline" className="flex items-center gap-2">
                  <Tag className="h-4 w-4" />
                  {cat}
                </Button>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-12 md:py-16">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="rounded-lg bg-gradient-to-r from-purple-600 to-cyan-500 p-8 md:p-12">
            <div className="grid gap-6 lg:grid-cols-2 items-center">
              <div className="text-white">
                <h2 className="text-2xl font-bold tracking-tight md:text-3xl">Subscribe to Our Newsletter</h2>
                <p className="mt-4 text-white/90">
                  Get the latest AI tools, affiliate marketing strategies, and income tips delivered straight to your
                  inbox.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                <input
                  type="email"
                  placeholder="Your email address"
                  className="px-4 py-2 rounded-md w-full bg-white/10 border border-white/20 text-white placeholder:text-white/60"
                />
                <Button className="bg-white text-purple-600 hover:bg-white/90">Subscribe</Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}
