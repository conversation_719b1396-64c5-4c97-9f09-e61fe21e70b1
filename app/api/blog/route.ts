import { createServerClient } from "@/lib/supabase/server"
import { NextResponse } from "next/server"

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const page = Number.parseInt(searchParams.get("page") || "1")
  const limit = Number.parseInt(searchParams.get("limit") || "6")
  const category = searchParams.get("category")
  const search = searchParams.get("search")

  const offset = (page - 1) * limit

  const supabase = createServerClient()

  // Log the query parameters for debugging
  console.log("Fetching posts with params:", { page, limit, category, search, offset })

  let query = supabase
    .from("blog_posts")
    .select("*, admin_users(full_name)", { count: "exact" })
    .eq("published", true)
    .order("published_at", { ascending: false })
    .range(offset, offset + limit - 1)

  if (category) {
    query = query.eq("category", category)
  }

  if (search) {
    query = query.or(`title.ilike.%${search}%,content.ilike.%${search}%,excerpt.ilike.%${search}%`)
  }

  const { data, count, error } = await query

  if (error) {
    console.error("Supabase query error:", error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  // Log the response for debugging
  console.log("API response:", { count, postsCount: data?.length })

  return NextResponse.json({
    posts: data || [],
    total: count || 0,
    page,
    limit,
    totalPages: Math.ceil((count || 0) / limit) || 1,
  })
}
