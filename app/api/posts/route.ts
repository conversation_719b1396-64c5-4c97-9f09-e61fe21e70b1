import { createServerClient } from "@/lib/supabase/server"
import { NextResponse } from "next/server"
import { cookies } from "next/headers"

export async function POST(request: Request) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient()

    // Try to get the session from Authorization header first
    const authHeader = request.headers.get('Authorization')
    let session = null

    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      const { data: { user }, error: userError } = await supabase.auth.getUser(token)

      if (user && !userError) {
        session = { user, access_token: token }
        console.log("Session found from Authorization header:", {
          user: user.id,
        })
      }
    }

    // If no session from header, try to get from cookies
    if (!session) {
      const { data: { session: cookieSession }, error: sessionError } = await supabase.auth.getSession()

      if (sessionError) {
        console.error("Session error:", sessionError)
        return NextResponse.json({ error: "Authentication error" }, { status: 401 })
      }

      session = cookieSession
      if (session) {
        console.log("Session found from cookies:", {
          user: session.user.id,
          expires_at: session.expires_at,
        })
      }
    }

    if (!session?.user) {
      console.error("No session or user found")
      return NextResponse.json({ error: "Unauthorized - No session" }, { status: 401 })
    }

    // Verify the user is an admin
    const { data: adminData, error: adminError } = await supabase
      .from('admin_users')
      .select('id')
      .eq('id', session.user.id)
      .single()

    if (adminError || !adminData) {
      console.error("Admin verification failed:", adminError)
      return NextResponse.json({ error: "Unauthorized - Not an admin" }, { status: 403 })
    }

    const body = await request.json()

    // Create post with minimal data first
    const { data: post, error: createError } = await supabase
      .from('blog_posts')
      .insert([
        {
          title: body.title,
          slug: body.slug,
          excerpt: body.excerpt,
          featured_image: body.featured_image,
          category: body.category,
          published: body.published,
          author_id: session.user.id,
          published_at: body.published ? new Date().toISOString() : null,
          content: body.content
        }
      ])
      .select()
      .single()

    if (createError) {
      console.error("Error creating post:", createError)
      return NextResponse.json({ error: createError.message }, { status: 500 })
    }

    if (!post) {
      return NextResponse.json({ error: "Failed to create post" }, { status: 500 })
    }

    return NextResponse.json({ data: post })
  } catch (error: any) {
    console.error("Error in post creation:", error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}