import Image from "next/image"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, Target, Users, Award } from "lucide-react"

export const metadata = {
  title: "About Us",
  description: "Learn about EarnifyAI and our mission to help you succeed with AI-powered affiliate marketing.",
}

export default function AboutPage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative py-14 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-cyan-50 dark:from-purple-950/20 dark:to-cyan-950/20 -z-10" />
        <div className="container px-4 md:px-6 mx-auto">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
            <div className="flex flex-col justify-center space-y-4">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none">
                  About{" "}
                  <span className="bg-gradient-to-r from-purple-600 to-cyan-500 bg-clip-text text-transparent">
                    EarnifyAI
                  </span>
                </h1>
                <p className="max-w-[600px] text-muted-foreground md:text-xl">
                  We're on a mission to help you navigate the world of AI-powered affiliate marketing and build
                  sustainable online income streams.
                </p>
              </div>
            </div>
            <div className="mx-auto lg:mx-0 relative">
              <Image
                src="https://images.unsplash.com/photo-1552664730-d307ca884978?q=80&w=500&auto=format&fit=crop"
                alt="EarnifyAI Team"
                width={500}
                height={500}
                className="rounded-lg shadow-xl"
                priority
              />
            </div>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="grid gap-12 lg:grid-cols-2 items-center">
            <div>
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl mb-6">Our Story</h2>
              <div className="space-y-4 text-muted-foreground">
                <p>
                  EarnifyAI was founded in 2025 with a simple mission: to help people navigate the rapidly evolving
                  landscape of AI tools and affiliate marketing.
                </p>
                <p>
                  We noticed that while there were plenty of resources on traditional affiliate marketing, very few
                  focused on how to leverage AI tools to automate and scale affiliate businesses.
                </p>
                <p>
                  Our team of affiliate marketing veterans and AI specialists came together to create a comprehensive
                  resource that bridges this gap, providing actionable strategies and tool recommendations.
                </p>
                <p>
                  Today, we're proud to have helped thousands of marketers and entrepreneurs build sustainable online
                  income streams through our guides, reviews, and community.
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center gap-4">
                  <Target className="h-8 w-8 text-purple-600" />
                  <div>
                    <CardTitle>Our Mission</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    To empower individuals with the knowledge and tools they need to build sustainable online income
                    streams through AI-powered affiliate marketing.
                  </CardDescription>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center gap-4">
                  <Users className="h-8 w-8 text-purple-600" />
                  <div>
                    <CardTitle>Our Community</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    We've built a thriving community of over 10,000 affiliate marketers who share insights, strategies,
                    and support each other's growth.
                  </CardDescription>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center gap-4">
                  <Award className="h-8 w-8 text-purple-600" />
                  <div>
                    <CardTitle>Our Expertise</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    Our team has over a decade of combined experience in affiliate marketing and has generated millions
                    in affiliate revenue.
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Our Values</h2>
            <p className="mt-4 text-xl text-muted-foreground">The principles that guide everything we do</p>
          </div>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {[
              {
                title: "Transparency",
                description: "We believe in full transparency about affiliate relationships and how we make money.",
              },
              {
                title: "Quality",
                description: "We only recommend tools and strategies that we've personally tested and found valuable.",
              },
              {
                title: "Education",
                description:
                  'We focus on education first, ensuring you understand the "why" behind our recommendations.',
              },
              {
                title: "Innovation",
                description:
                  "We constantly explore new AI tools and strategies to keep our community ahead of the curve.",
              },
              {
                title: "Community",
                description: "We believe in the power of community and foster an environment of sharing and support.",
              },
              {
                title: "Results",
                description: "We measure our success by the results our community achieves with our guidance.",
              },
            ].map((value) => (
              <Card key={value.title}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-purple-600" />
                    {value.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{value.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
