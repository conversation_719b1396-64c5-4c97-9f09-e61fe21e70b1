"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import type { Session, User } from "@supabase/supabase-js"
import { supabase } from "@/lib/supabase/client"

type AuthContextType = {
  user: User | null
  session: Session | null
  isLoading: boolean
  signIn: (
    email: string,
    password: string,
  ) => Promise<{
    error: any | null
    data: any | null
  }>
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const setData = async () => {
      try {
        const {
          data: { session },
          error,
        } = await supabase.auth.getSession()

        if (error) {
          console.error("Error getting session:", error)
          setIsLoading(false)
          return
        }

        setSession(session)
        setUser(session?.user ?? null)

        // If we have a user, check if they're an admin
        if (session?.user) {
          const { data: adminData, error: adminError } = await supabase
            .from("admin_users")
            .select("*")
            .eq("id", session.user.id)
            .single()

          if (adminError || !adminData) {
            console.error("User is not an admin:", adminError)
            await supabase.auth.signOut()
            setUser(null)
            setSession(null)
          }
        }
      } catch (err) {
        console.error("Error in setData:", err)
      } finally {
        setIsLoading(false)
      }
    }

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (_event, session) => {
      setSession(session)
      setUser(session?.user ?? null)

      // If we have a user, check if they're an admin
      if (session?.user) {
        const { data: adminData, error: adminError } = await supabase
          .from("admin_users")
          .select("*")
          .eq("id", session.user.id)
          .single()

        if (adminError || !adminData) {
          console.error("User is not an admin:", adminError)
          await supabase.auth.signOut()
          setUser(null)
          setSession(null)
        }
      }

      setIsLoading(false)
    })

    setData()

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        return { data: null, error }
      }

      if (!data?.user) {
        return {
          data: null,
          error: { message: "No user returned from authentication" },
        }
      }

      // Check if user is an admin
      const { data: adminData, error: adminError } = await supabase
        .from("admin_users")
        .select("*")
        .eq("id", data.user.id)
        .single()

      if (adminError || !adminData) {
        // If not an admin, sign them out
        await supabase.auth.signOut()
        return {
          data: null,
          error: { message: "You are not authorized to access the admin area." },
        }
      }

      router.push("/admin/dashboard")
      return { data, error: null }
    } catch (err: any) {
      console.error("Error in signIn:", err)
      return {
        data: null,
        error: { message: err.message || "An error occurred during sign in" },
      }
    }
  }

  const signOut = async () => {
    try {
      await supabase.auth.signOut()
      router.push("/admin/login")
    } catch (err) {
      console.error("Error signing out:", err)
    }
  }

  const value = {
    user,
    session,
    isLoading,
    signIn,
    signOut,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
