-- Enable Row Level Security
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;

-- Create policy for inserting posts (admin users only)
CREATE POLICY "Enable insert for authenticated admin users" ON blog_posts
FOR INSERT TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM admin_users
    WHERE admin_users.id = auth.uid()
  )
);

-- Create policy for selecting posts
CREATE POLICY "Enable read access for all users" ON blog_posts
FOR SELECT TO authenticated
USING (true);

-- Create policy for updating posts (admin users only)
CREATE POLICY "Enable update for authenticated admin users" ON blog_posts
FOR UPDATE TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM admin_users
    WHERE admin_users.id = auth.uid()
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM admin_users
    WHERE admin_users.id = auth.uid()
  )
);

-- Create policy for deleting posts (admin users only)
CREATE POLICY "Enable delete for authenticated admin users" ON blog_posts
FOR DELETE TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM admin_users
    WHERE admin_users.id = auth.uid()
  )
); 