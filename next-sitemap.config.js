/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.SITE_URL || "https://earnifyai.com",
  generateRobotsTxt: true,
  robotsTxtOptions: {
    additionalSitemaps: [
      "https://earnifyai.com/server-sitemap.xml", // For dynamic routes
    ],
    policies: [
      {
        userAgent: "*",
        allow: "/",
        disallow: ["/admin", "/admin/*"],
      },
    ],
  },
  exclude: ["/admin/*", "/server-sitemap.xml"],
  // Change frequency and priority based on page importance
  changefreq: "weekly",
  priority: 0.7,
  sitemapSize: 5000,
  // Generate a sitemap for each locale if you have a multi-language site
  // alternateRefs: [
  //   {
  //     href: 'https://earnifyai.com',
  //     hreflang: 'en',
  //   },
  // ],
}
