"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { ArrowLeft, ArrowRight } from "lucide-react"
import { supabase } from "@/lib/supabase/client"

type Post = {
  id: string
  title: string
  slug: string
  featured_image: string | null
}

export function PostNavigation({ currentPostId }: { currentPostId: string }) {
  const [prevPost, setPrevPost] = useState<Post | null>(null)
  const [nextPost, setNextPost] = useState<Post | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchAdjacentPosts = async () => {
      setLoading(true)

      try {
        // First, get all published posts ordered by date
        const { data: allPosts, error } = await supabase
          .from("blog_posts")
          .select("id, title, slug, featured_image")
          .eq("published", true)
          .order("published_at", { ascending: false })
          .order("created_at", { ascending: false })

        if (error) {
          console.error("Error fetching posts:", error)
          setLoading(false)
          return
        }

        if (!allPosts || allPosts.length === 0) {
          setLoading(false)
          return
        }

        // Find the index of the current post
        const currentIndex = allPosts.findIndex((post) => post.id === currentPostId)

        if (currentIndex === -1) {
          console.error("Current post not found in the list")
          setLoading(false)
          return
        }

        // Get previous and next posts based on the index
        const previousPost = currentIndex < allPosts.length - 1 ? allPosts[currentIndex + 1] : null
        const nextPost = currentIndex > 0 ? allPosts[currentIndex - 1] : null

        setPrevPost(previousPost)
        setNextPost(nextPost)
      } catch (error) {
        console.error("Error in fetchAdjacentPosts:", error)
      } finally {
        setLoading(false)
      }
    }

    if (currentPostId) {
      fetchAdjacentPosts()
    }
  }, [currentPostId])

  if (loading) {
    return null
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div>
        {prevPost ? (
          <Link href={`/blog/${prevPost.slug}`}>
            <Card className="h-full hover:shadow-md transition-shadow">
              <CardContent className="p-4 flex items-center gap-4">
                {prevPost.featured_image && (
                  <div className="relative h-16 w-16 flex-shrink-0 rounded-md overflow-hidden">
                    <Image
                      src={prevPost.featured_image || "/placeholder.svg"}
                      alt={prevPost.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center text-sm text-muted-foreground mb-1">
                    <ArrowLeft className="mr-1 h-4 w-4" />
                    <span>Previous Post</span>
                  </div>
                  <h4 className="font-medium line-clamp-1">{prevPost.title}</h4>
                </div>
              </CardContent>
            </Card>
          </Link>
        ) : (
          <Card className="h-full bg-muted/50">
            <CardContent className="p-4 flex items-center">
              <p className="text-muted-foreground">No previous post</p>
            </CardContent>
          </Card>
        )}
      </div>
      <div>
        {nextPost ? (
          <Link href={`/blog/${nextPost.slug}`}>
            <Card className="h-full hover:shadow-md transition-shadow">
              <CardContent className="p-4 flex items-center gap-4">
                <div className="flex-1 min-w-0 text-right">
                  <div className="flex items-center justify-end text-sm text-muted-foreground mb-1">
                    <span>Next Post</span>
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </div>
                  <h4 className="font-medium line-clamp-1">{nextPost.title}</h4>
                </div>
                {nextPost.featured_image && (
                  <div className="relative h-16 w-16 flex-shrink-0 rounded-md overflow-hidden">
                    <Image
                      src={nextPost.featured_image || "/placeholder.svg"}
                      alt={nextPost.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </Link>
        ) : (
          <Card className="h-full bg-muted/50">
            <CardContent className="p-4 flex items-center justify-end">
              <p className="text-muted-foreground">No next post</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
