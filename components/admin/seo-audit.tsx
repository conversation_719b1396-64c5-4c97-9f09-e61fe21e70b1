"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, XCircle, AlertTriangle, Loader2 } from "lucide-react"

export function SeoAudit() {
  const [url, setUrl] = useState("")
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<any>(null)

  const runAudit = async () => {
    setLoading(true)

    // This is a simplified audit - in a real implementation, you'd call an API
    // that performs a more comprehensive check
    setTimeout(() => {
      const mockResults = {
        title: {
          value: "Getting Started with AI Tools for Affiliate Marketing | EarnifyAI",
          length: 59,
          status: "good", // good, warning, error
          message: "Title length is optimal (59 characters)",
        },
        description: {
          value:
            "Learn how to leverage AI tools to boost your affiliate marketing strategy and increase your online income with our comprehensive guide.",
          length: 124,
          status: "good",
          message: "Description length is good (124 characters)",
        },
        headings: {
          h1Count: 1,
          status: "good",
          message: "Page has exactly one H1 tag",
        },
        images: {
          count: 5,
          withAlt: 4,
          status: "warning",
          message: "1 image is missing alt text",
        },
        keywords: {
          primary: "AI tools for affiliate marketing",
          density: 1.8,
          status: "good",
          message: "Keyword density is optimal (1.8%)",
        },
        links: {
          internal: 12,
          external: 3,
          status: "good",
          message: "Good balance of internal and external links",
        },
        performance: {
          wordCount: 1250,
          status: "good",
          message: "Content length is good (1250 words)",
        },
        mobile: {
          status: "good",
          message: "Page is mobile-friendly",
        },
        schema: {
          status: "error",
          message: "Missing Article schema markup",
        },
      }

      setResults(mockResults)
      setLoading(false)
    }, 2000)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>SEO Audit Tool</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex gap-2">
            <Input placeholder="Enter page URL to audit" value={url} onChange={(e) => setUrl(e.target.value)} />
            <Button onClick={runAudit} disabled={loading || !url}>
              {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Run Audit
            </Button>
          </div>

          {results && (
            <div className="space-y-4 mt-6">
              <h3 className="text-lg font-medium">Audit Results</h3>

              <div className="space-y-3">
                <AuditItem
                  title="Page Title"
                  status={results.title.status}
                  message={results.title.message}
                  value={results.title.value}
                />

                <AuditItem
                  title="Meta Description"
                  status={results.description.status}
                  message={results.description.message}
                  value={results.description.value}
                />

                <AuditItem
                  title="Headings Structure"
                  status={results.headings.status}
                  message={results.headings.message}
                />

                <AuditItem title="Images" status={results.images.status} message={results.images.message} />

                <AuditItem
                  title="Keyword Usage"
                  status={results.keywords.status}
                  message={results.keywords.message}
                  value={`Primary keyword: "${results.keywords.primary}"`}
                />

                <AuditItem title="Links" status={results.links.status} message={results.links.message} />

                <AuditItem title="Content" status={results.performance.status} message={results.performance.message} />

                <AuditItem
                  title="Mobile Friendliness"
                  status={results.mobile.status}
                  message={results.mobile.message}
                />

                <AuditItem title="Schema Markup" status={results.schema.status} message={results.schema.message} />
              </div>

              <div className="pt-4 border-t mt-4">
                <h3 className="text-lg font-medium mb-2">Recommendations</h3>
                <ul className="space-y-2">
                  {results.schema.status === "error" && (
                    <li className="flex items-start">
                      <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0" />
                      <span>Add Article schema markup to improve rich snippet opportunities</span>
                    </li>
                  )}
                  {results.images.status === "warning" && (
                    <li className="flex items-start">
                      <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0" />
                      <span>Add alt text to all images for better accessibility and SEO</span>
                    </li>
                  )}
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                    <span>Consider adding more internal links to related content</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                    <span>Add FAQ section to target featured snippets</span>
                  </li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

function AuditItem({
  title,
  status,
  message,
  value,
}: { title: string; status: string; message: string; value?: string }) {
  return (
    <div className="border rounded-md p-3">
      <div className="flex items-start justify-between">
        <div className="flex items-start">
          {status === "good" && <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />}
          {status === "warning" && <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 flex-shrink-0" />}
          {status === "error" && <XCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" />}
          <div>
            <h4 className="font-medium">{title}</h4>
            <p className="text-sm text-muted-foreground">{message}</p>
            {value && <p className="text-xs mt-1 text-muted-foreground">{value}</p>}
          </div>
        </div>
        <div>
          <span
            className={`text-xs px-2 py-1 rounded-full ${
              status === "good"
                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                : status === "warning"
                  ? "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400"
                  : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
            }`}
          >
            {status.toUpperCase()}
          </span>
        </div>
      </div>
    </div>
  )
}
