"use client"

import type React from "react"
import { usePathname } from "next/navigation"
import Header from "@/components/header"
import Footer from "@/components/footer"
import { BackToTop } from "@/components/back-to-top"

interface ConditionalLayoutProps {
  children: React.ReactNode
}

export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname()
  
  // Check if the current path is an admin route
  const isAdminRoute = pathname?.startsWith('/admin')

  if (isAdminRoute) {
    // For admin routes, render children without main site header/footer
    return <>{children}</>
  }

  // For non-admin routes, render with main site layout
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">{children}</main>
      <Footer />
      <BackToTop />
    </div>
  )
}
