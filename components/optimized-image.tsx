"use client"

import Image from "next/image"
import { useState } from "react"
import { cn } from "@/lib/utils"

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  fill?: boolean
  sizes?: string
  priority?: boolean
  className?: string
  fallbackSrc?: string
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  fill = false,
  sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
  priority = false,
  className,
  fallbackSrc = "/placeholder.svg",
}: OptimizedImageProps) {
  const [isError, setIsError] = useState(false)

  return (
    <div className={cn("relative overflow-hidden", fill && "w-full h-full", className)}>
      <Image
        src={isError ? fallbackSrc : src}
        alt={alt}
        width={!fill ? width : undefined}
        height={!fill ? height : undefined}
        fill={fill}
        sizes={sizes}
        priority={priority}
        quality={80}
        loading={priority ? "eager" : "lazy"}
        onError={() => setIsError(true)}
        className={cn("object-cover transition-opacity duration-300", className)}
      />
    </div>
  )
}
