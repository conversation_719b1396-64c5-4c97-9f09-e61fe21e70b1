"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON>, CardDescription, Card<PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

interface BlogCardProps {
  post: {
    id: string
    title: string
    slug: string
    excerpt: string | null
    featured_image: string | null
    category: string | null
    published_at: string | null
    created_at: string
    content: string | null
  }
  imageUrl: string
  calculateReadTime: (content: string) => number
}

export function BlogCard({ post, imageUrl, calculateReadTime }: BlogCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <Card
      className="overflow-hidden flex flex-col h-full transition-all duration-300 hover:shadow-lg border border-border/50 hover:border-primary/20"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Link href={`/blog/${post.slug}`} className="relative aspect-video block overflow-hidden">
        <Image
          src={imageUrl || "/placeholder.svg"}
          alt={post.title}
          fill
          className={cn("object-cover transition-transform duration-700", isHovered ? "scale-110" : "scale-100")}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 transition-opacity duration-300 hover:opacity-100" />
        {post.category && (
          <div className="absolute top-2 left-2 bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-medium">
            {post.category}
          </div>
        )}
      </Link>
      <CardHeader className="flex-1">
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
          <span>{format(new Date(post.published_at || post.created_at), "MMM d, yyyy")}</span>
          <span>•</span>
          <span>{calculateReadTime(post.content || "")} min read</span>
        </div>
        <Link href={`/blog/${post.slug}`} className="hover:underline group">
          <CardTitle className="line-clamp-2 group-hover:text-primary transition-colors duration-200">
            {post.title}
          </CardTitle>
        </Link>
        <CardDescription className="line-clamp-3 mt-2">{post.excerpt}</CardDescription>
      </CardHeader>
      <CardFooter>
        <Link href={`/blog/${post.slug}`} className="w-full">
          <Button variant="outline" className="w-full group hover:bg-primary/5 transition-all duration-300">
            Read More
            <ArrowRight
              className={cn("ml-2 h-4 w-4 transition-transform duration-300", isHovered ? "translate-x-1" : "")}
            />
          </Button>
        </Link>
      </CardFooter>
    </Card>
  )
}
