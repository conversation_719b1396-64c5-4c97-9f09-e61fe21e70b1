"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface ModernCardProps extends React.ComponentProps<typeof Card> {
  hoverEffect?: "lift" | "glow" | "border" | "none"
  children: React.ReactNode
}

export function ModernCard({ hoverEffect = "lift", className, children, ...props }: ModernCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <Card
      className={cn(
        "transition-all duration-300 ease-in-out",
        hoverEffect === "lift" && "hover:-translate-y-1 hover:shadow-lg",
        hoverEffect === "glow" && "hover:shadow-[0_0_15px_rgba(124,58,237,0.3)]",
        hoverEffect === "border" && "hover:border-primary/50",
        className,
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      {...props}
    >
      {children}
    </Card>
  )
}

export { CardContent, CardDescription, CardFooter, CardHeader, CardTitle }
