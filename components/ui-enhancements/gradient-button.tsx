"use client"

import type React from "react"

import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface GradientButtonProps extends React.ComponentProps<typeof Button> {
  gradientFrom?: string
  gradientTo?: string
  hoverGradientFrom?: string
  hoverGradientTo?: string
  children: React.ReactNode
}

export function GradientButton({
  gradientFrom = "from-purple-600",
  gradientTo = "to-cyan-500",
  hoverGradientFrom = "from-purple-700",
  hoverGradientTo = "to-cyan-600",
  className,
  children,
  ...props
}: GradientButtonProps) {
  return (
    <Button
      className={cn(
        "relative overflow-hidden transition-all duration-300",
        "bg-gradient-to-r",
        gradientFrom,
        gradientTo,
        "hover:bg-gradient-to-r",
        hoverGradientFrom,
        hoverGradientTo,
        "after:absolute after:inset-0 after:bg-gradient-to-r",
        `after:${hoverGradientFrom}`,
        `after:${hoverGradientTo}`,
        "after:opacity-0 hover:after:opacity-100 after:transition-opacity after:duration-300",
        "shadow-md hover:shadow-lg",
        className,
      )}
      {...props}
    >
      <span className="relative z-10">{children}</span>
    </Button>
  )
}
