"use client"

import type React from "react"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { SearchIcon } from "lucide-react"
import { useRouter } from "next/navigation"
import { useSearchParams } from "next/navigation"

export const Search = () => {
  const [searchValue, setSearchValue] = useState("")
  const router = useRouter()
  const searchParams = useSearchParams()

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(event.target.value)
    const value = event.target.value

    const params = new URLSearchParams(searchParams)

    if (value) {
      params.set("search", value)
    } else {
      params.delete("search")
    }

    router.push(`/blog?${params.toString()}`)
  }

  return (
    <div className="relative w-1/4">
      <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      <Input
        type="search"
        placeholder="Search topics..."
        className="w-full pl-9 pr-3 py-2 rounded-md border border-input bg-background shadow-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-50"
        value={searchValue}
        onChange={handleSearch}
      />
    </div>
  )
}
