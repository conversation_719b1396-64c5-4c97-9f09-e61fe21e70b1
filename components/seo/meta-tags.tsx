"use client"

import Head from "next/head"
import { usePathname } from "next/navigation"

interface MetaTagsProps {
  title?: string
  description?: string
  canonicalUrl?: string
  ogImage?: string
  ogType?: "website" | "article"
  keywords?: string[]
  author?: string
  publishedTime?: string
  modifiedTime?: string
}

export function MetaTags({
  title = "EarnifyAI | AI Tools & Affiliate Marketing Strategies",
  description = "Discover the best AI tools and smart strategies to make money online. Learn affiliate marketing, productivity hacks, and digital income tips — all in one place.",
  canonicalUrl,
  ogImage = "https://earnifyai.com/og-image.jpg",
  ogType = "website",
  keywords = ["affiliate marketing", "AI tools", "make money online", "digital income", "productivity hacks"],
  author = "EarnifyAI Team",
  publishedTime,
  modifiedTime,
}: MetaTagsProps) {
  const pathname = usePathname()
  const siteUrl = "https://earnifyai.com"
  const fullUrl = canonicalUrl || `${siteUrl}${pathname}`

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(", ")} />
      <meta name="author" content={author} />

      {/* Canonical Tag */}
      <link rel="canonical" href={fullUrl} />

      {/* Open Graph Tags */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:type" content={ogType} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:site_name" content="EarnifyAI" />

      {/* Twitter Card Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />

      {/* Article Specific Tags (for blog posts) */}
      {ogType === "article" && publishedTime && <meta property="article:published_time" content={publishedTime} />}
      {ogType === "article" && modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
      {ogType === "article" && <meta property="article:section" content="Affiliate Marketing" />}
      {ogType === "article" &&
        keywords.map((keyword, index) => <meta key={index} property="article:tag" content={keyword} />)}
    </Head>
  )
}
