import Script from "next/script"

interface BlogSchemaProps {
  title: string
  description: string
  datePublished: string
  dateModified?: string
  authorName: string
  imageUrl: string
  url: string
  keywords?: string[]
}

export function BlogSchema({
  title,
  description,
  datePublished,
  dateModified,
  authorName,
  imageUrl,
  url,
  keywords = [],
}: BlogSchemaProps) {
  const schema = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    headline: title,
    description,
    image: imageUrl,
    datePublished,
    dateModified: dateModified || datePublished,
    author: {
      "@type": "Person",
      name: authorN<PERSON>,
    },
    publisher: {
      "@type": "Organization",
      name: "EarnifyAI",
      logo: {
        "@type": "ImageObject",
        url: "https://earnifyai.com/logo.png",
      },
    },
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": url,
    },
    keywords: keywords.join(", "),
  }

  return (
    <Script id="blog-schema" type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }} />
  )
}
